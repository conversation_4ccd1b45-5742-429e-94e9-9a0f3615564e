/**
 * Tests for individual spaceship components
 */

import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import RocketSpaceship from '@/components/RocketSpaceship';
import UFOSpaceship from '@/components/UFOSpaceship';

// Mock GSAP and related hooks
vi.mock('@/hooks/useGSAP', () => ({
  useGSAP: () => ({
    gsap: {
      timeline: () => ({
        set: vi.fn().mockReturnThis(),
        to: vi.fn().mockReturnThis(),
        fromTo: vi.fn().mockReturnThis(),
        add: vi.fn().mockReturnThis(),
      }),
      set: vi.fn(),
    },
    animate: vi.fn(),
    isAvailable: true,
  }),
}));

vi.mock('@/components/ErrorBoundary', () => ({
  useErrorHandler: () => ({
    handleError: vi.fn(),
  }),
}));

vi.mock('@/utils', () => ({
  getAccessibilityConfig: () => ({
    reduceMotion: false,
  }),
}));

describe('RocketSpaceship', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders rocket SVG with correct structure', () => {
    const { container } = render(<RocketSpaceship />);
    
    const svg = container.querySelector('svg');
    expect(svg).toBeInTheDocument();
    
    // Check for rocket-specific elements
    const rocketBody = container.querySelector('rect');
    expect(rocketBody).toBeInTheDocument();
    
    const noseCone = container.querySelector('polygon');
    expect(noseCone).toBeInTheDocument();
  });

  it('handles hover events', () => {
    const onHover = vi.fn();
    const { container } = render(<RocketSpaceship onHover={onHover} />);
    
    const rocketContainer = container.firstChild as HTMLElement;
    
    fireEvent.mouseEnter(rocketContainer);
    expect(onHover).toHaveBeenCalledWith(true);
    
    fireEvent.mouseLeave(rocketContainer);
    expect(onHover).toHaveBeenCalledWith(false);
  });

  it('applies correct size classes', () => {
    const { container } = render(<RocketSpaceship size="lg" />);
    
    const svg = container.querySelector('svg');
    expect(svg).toHaveAttribute('width', '80');
    expect(svg).toHaveAttribute('height', '100');
  });

  it('includes thruster flame elements', () => {
    const { container } = render(<RocketSpaceship />);
    
    // Check for thruster flame gradients
    const thrusterGradient = container.querySelector('#thruster-flame');
    expect(thrusterGradient).toBeInTheDocument();
  });
});

describe('UFOSpaceship', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders UFO SVG with correct structure', () => {
    const { container } = render(<UFOSpaceship />);
    
    const svg = container.querySelector('svg');
    expect(svg).toBeInTheDocument();
    
    // Check for UFO-specific elements
    const saucer = container.querySelector('ellipse');
    expect(saucer).toBeInTheDocument();
    
    const tractorBeam = container.querySelector('polygon');
    expect(tractorBeam).toBeInTheDocument();
  });

  it('handles hover events', () => {
    const onHover = vi.fn();
    const { container } = render(<UFOSpaceship onHover={onHover} />);
    
    const ufoContainer = container.firstChild as HTMLElement;
    
    fireEvent.mouseEnter(ufoContainer);
    expect(onHover).toHaveBeenCalledWith(true);
    
    fireEvent.mouseLeave(ufoContainer);
    expect(onHover).toHaveBeenCalledWith(false);
  });

  it('applies correct size classes', () => {
    const { container } = render(<UFOSpaceship size="lg" />);
    
    const svg = container.querySelector('svg');
    expect(svg).toHaveAttribute('width', '88');
    expect(svg).toHaveAttribute('height', '72');
  });

  it('includes blinking lights', () => {
    const { container } = render(<UFOSpaceship />);
    
    // Check for multiple light circles
    const lights = container.querySelectorAll('circle');
    expect(lights.length).toBeGreaterThan(0);
  });

  it('includes tractor beam gradient', () => {
    const { container } = render(<UFOSpaceship />);
    
    const beamGradient = container.querySelector('#tractor-beam');
    expect(beamGradient).toBeInTheDocument();
  });
});
