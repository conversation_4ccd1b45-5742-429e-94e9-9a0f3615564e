/**
 * Rocket-style spaceship SVG component with GSAP animations
 */

import { useRef, useCallback, useEffect } from 'react';
import { useGSAP } from '@/hooks/useGSAP';
import { useErrorHandler } from '@/components/ErrorBoundary';
import { getAccessibilityConfig } from '@/utils';
import {
  ANIMATION_DURATIONS,
  ANIMATION_EASINGS,
  COLOR_VARIANTS,
  CONTACT_CONFIG
} from '@/constants';

interface RocketSpaceshipProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  onHover?: (isHovering: boolean) => void;
}

export default function RocketSpaceship({ 
  className = '', 
  size = 'md',
  onHover 
}: RocketSpaceshipProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const rocketRef = useRef<SVGSVGElement>(null);
  const thrusterRef = useRef<SVGGElement>(null);
  const glowRef = useRef<SVGGElement>(null);
  const particlesRef = useRef<SVGGElement>(null);
  const hoverTimelineRef = useRef<any>(null);
  const thrusterTimelineRef = useRef<any>(null);

  const { gsap, animate, isAvailable } = useGSAP();
  const { handleError } = useErrorHandler();

  // Size configurations
  const sizeConfig = {
    sm: { width: 48, height: 64, viewBox: '0 0 48 64' },
    md: { width: 64, height: 80, viewBox: '0 0 64 80' },
    lg: { width: 80, height: 100, viewBox: '0 0 80 100' }
  };

  const currentSize = sizeConfig[size];

  const startThrusterAnimation = useCallback(() => {
    try {
      if (!isAvailable || !gsap || !thrusterRef.current) return;

      const accessibility = getAccessibilityConfig();
      if (accessibility.reduceMotion) return;

      // Continuous thruster flame animation
      thrusterTimelineRef.current = gsap.timeline({ repeat: -1 });
      
      thrusterTimelineRef.current
        .to(thrusterRef.current, {
          scaleY: 1.3,
          opacity: 0.9,
          duration: 0.3,
          ease: ANIMATION_EASINGS.POWER2_IN_OUT,
        })
        .to(thrusterRef.current, {
          scaleY: 0.8,
          opacity: 0.6,
          duration: 0.4,
          ease: ANIMATION_EASINGS.POWER2_IN_OUT,
        });

    } catch (error) {
      handleError(error as Error);
    }
  }, [isAvailable, gsap, handleError]);

  const handleMouseEnter = useCallback(() => {
    try {
      if (!isAvailable || !gsap || !rocketRef.current || !glowRef.current) return;

      const accessibility = getAccessibilityConfig();
      if (accessibility.reduceMotion) return;

      onHover?.(true);

      // Kill existing hover animation
      if (hoverTimelineRef.current && typeof hoverTimelineRef.current.kill === 'function') {
        hoverTimelineRef.current.kill();
      }

      hoverTimelineRef.current = gsap.timeline();

      // Enhanced hover effects
      hoverTimelineRef.current
        .to(rocketRef.current, {
          scale: CONTACT_CONFIG.SPACESHIP_HOVER_SCALE,
          rotation: 5,
          duration: ANIMATION_DURATIONS.FAST,
          ease: ANIMATION_EASINGS.BACK_OUT,
        }, 0)
        .to(glowRef.current, {
          opacity: 0.8,
          scale: CONTACT_CONFIG.SPACESHIP_HOVER_GLOW_INTENSITY,
          duration: ANIMATION_DURATIONS.FAST,
          ease: ANIMATION_EASINGS.POWER2_OUT,
        }, 0)
        .to(thrusterRef.current, {
          scaleY: 1.5,
          opacity: 1,
          duration: ANIMATION_DURATIONS.FAST,
          ease: ANIMATION_EASINGS.POWER2_OUT,
        }, 0);

    } catch (error) {
      handleError(error as Error);
    }
  }, [isAvailable, gsap, onHover, handleError]);

  const handleMouseLeave = useCallback(() => {
    try {
      if (!isAvailable || !gsap || !rocketRef.current || !glowRef.current) return;

      const accessibility = getAccessibilityConfig();
      if (accessibility.reduceMotion) return;

      onHover?.(false);

      // Kill existing hover animation
      if (hoverTimelineRef.current && typeof hoverTimelineRef.current.kill === 'function') {
        hoverTimelineRef.current.kill();
      }

      hoverTimelineRef.current = gsap.timeline();

      // Return to normal state
      hoverTimelineRef.current
        .to(rocketRef.current, {
          scale: 1,
          rotation: 0,
          duration: ANIMATION_DURATIONS.NORMAL,
          ease: ANIMATION_EASINGS.POWER2_OUT,
        }, 0)
        .to(glowRef.current, {
          opacity: 0.3,
          scale: 1,
          duration: ANIMATION_DURATIONS.NORMAL,
          ease: ANIMATION_EASINGS.POWER2_OUT,
        }, 0)
        .to(thrusterRef.current, {
          scaleY: 1,
          opacity: 0.7,
          duration: ANIMATION_DURATIONS.NORMAL,
          ease: ANIMATION_EASINGS.POWER2_OUT,
        }, 0);

    } catch (error) {
      handleError(error as Error);
    }
  }, [isAvailable, gsap, onHover, handleError]);

  useEffect(() => {
    if (isAvailable) {
      startThrusterAnimation();
    }

    return () => {
      if (hoverTimelineRef.current && typeof hoverTimelineRef.current.kill === 'function') {
        hoverTimelineRef.current.kill();
      }
      if (thrusterTimelineRef.current && typeof thrusterTimelineRef.current.kill === 'function') {
        thrusterTimelineRef.current.kill();
      }
    };
  }, [isAvailable, startThrusterAnimation]);

  return (
    <div
      ref={containerRef}
      className={`inline-block cursor-pointer ${className}`}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      style={{ filter: 'drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3))' }}
    >
      <svg
        ref={rocketRef}
        width={currentSize.width}
        height={currentSize.height}
        viewBox={currentSize.viewBox}
        className="overflow-visible"
        style={{ transformOrigin: 'center center' }}
      >
        {/* Glow Effect */}
        <defs>
          <radialGradient id="rocket-glow" cx="50%" cy="50%" r="60%">
            <stop offset="0%" stopColor={COLOR_VARIANTS.PINK} stopOpacity="0.6" />
            <stop offset="50%" stopColor={COLOR_VARIANTS.BLUE} stopOpacity="0.3" />
            <stop offset="100%" stopColor="transparent" stopOpacity="0" />
          </radialGradient>
          
          <linearGradient id="rocket-body" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" stopColor={COLOR_VARIANTS.WHITE} />
            <stop offset="50%" stopColor="#E5E5E5" />
            <stop offset="100%" stopColor="#CCCCCC" />
          </linearGradient>
          
          <linearGradient id="rocket-nose" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" stopColor={COLOR_VARIANTS.PINK} />
            <stop offset="100%" stopColor="#FF1A4D" />
          </linearGradient>
          
          <linearGradient id="thruster-flame" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" stopColor="#FFD700" />
            <stop offset="50%" stopColor="#FF6B35" />
            <stop offset="100%" stopColor={COLOR_VARIANTS.PINK} />
          </linearGradient>
        </defs>

        {/* Glow Background */}
        <g ref={glowRef} opacity="0.3">
          <circle
            cx={currentSize.width / 2}
            cy={currentSize.height / 2}
            r={currentSize.width * 0.8}
            fill="url(#rocket-glow)"
          />
        </g>

        {/* Rocket Body */}
        <g>
          {/* Main Body */}
          <rect
            x={currentSize.width * 0.3}
            y={currentSize.height * 0.25}
            width={currentSize.width * 0.4}
            height={currentSize.height * 0.5}
            rx="4"
            fill="url(#rocket-body)"
            stroke={COLOR_VARIANTS.BLUE}
            strokeWidth="1"
          />
          
          {/* Nose Cone */}
          <polygon
            points={`${currentSize.width * 0.3},${currentSize.height * 0.25} ${currentSize.width * 0.7},${currentSize.height * 0.25} ${currentSize.width * 0.5},${currentSize.height * 0.1}`}
            fill="url(#rocket-nose)"
            stroke={COLOR_VARIANTS.PINK}
            strokeWidth="1"
          />
          
          {/* Window */}
          <circle
            cx={currentSize.width / 2}
            cy={currentSize.height * 0.35}
            r={currentSize.width * 0.08}
            fill={COLOR_VARIANTS.BLUE}
            opacity="0.8"
          />
          
          {/* Fins */}
          <polygon
            points={`${currentSize.width * 0.25},${currentSize.height * 0.65} ${currentSize.width * 0.3},${currentSize.height * 0.75} ${currentSize.width * 0.3},${currentSize.height * 0.55}`}
            fill={COLOR_VARIANTS.BLUE}
          />
          <polygon
            points={`${currentSize.width * 0.75},${currentSize.height * 0.65} ${currentSize.width * 0.7},${currentSize.height * 0.75} ${currentSize.width * 0.7},${currentSize.height * 0.55}`}
            fill={COLOR_VARIANTS.BLUE}
          />
        </g>

        {/* Thruster Flames */}
        <g ref={thrusterRef} opacity="0.7" style={{ transformOrigin: `${currentSize.width / 2}px ${currentSize.height * 0.75}px` }}>
          <polygon
            points={`${currentSize.width * 0.35},${currentSize.height * 0.75} ${currentSize.width * 0.65},${currentSize.height * 0.75} ${currentSize.width * 0.5},${currentSize.height * 0.95}`}
            fill="url(#thruster-flame)"
            opacity="0.9"
          />
          <polygon
            points={`${currentSize.width * 0.4},${currentSize.height * 0.75} ${currentSize.width * 0.6},${currentSize.height * 0.75} ${currentSize.width * 0.5},${currentSize.height * 0.85}`}
            fill="#FFD700"
            opacity="0.7"
          />
        </g>
      </svg>
    </div>
  );
}
